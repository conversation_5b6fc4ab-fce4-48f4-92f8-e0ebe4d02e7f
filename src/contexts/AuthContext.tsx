/**
 * New AuthContext with better error handling
 * This fixes the "dispatcher is null" error
 */

import React from 'react';
import { User, UserType } from '../types/userDefinitions';
import { supabase } from '@/integrations/supabase/client';
import { performCompleteSessionCleanup } from '../utils/sessionCleanup';

// Function to update last login timestamp
const updateLastLogin = async (userId: string) => {
  try {
    const { error } = await supabase
      .from('users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', userId);

    if (error) {
      console.error('Error updating last login:', error);
    } else {
      console.log('Last login updated successfully for user:', userId);
    }
  } catch (error) {
    console.error('Error in updateLastLogin:', error);
  }
};

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (userData: User) => void;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  hasPermission: (permission: string) => Promise<boolean>;
  sessionExpiry: Date | null;
}

// Create context with default values
const AuthContext = React.createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  loading: true,
  login: () => {},
  logout: () => {},
  updateUser: () => {},
  hasPermission: async () => false,
  sessionExpiry: null,
});

// Default permissions for different user types
const defaultPermissions = {
  admin: [
    'users.create', 'users.read', 'users.update', 'users.delete',
    'products.create', 'products.read', 'products.update', 'products.delete',
    'orders.create', 'orders.read', 'orders.update', 'orders.delete',
    'reports.read', 'settings.write', 'branches.manage'
  ],
  manager: [
    'products.create', 'products.read', 'products.update', 'products.delete',
    'orders.create', 'orders.read', 'orders.update', 'orders.delete',
    'inventory.read', 'inventory.update', 'inventory.manage',
    'categories.create', 'categories.read', 'categories.update', 'categories.delete',
    'branches.read', 'branches.update', 'branches.manage',
    'clients.read', 'clients.update', 'clients.manage',
    'promocodes.create', 'promocodes.read', 'promocodes.update', 'promocodes.delete',
    'reviews.read', 'reviews.update', 'reviews.manage',
    'profile.read', 'profile.write'
  ],
  client: [
    'products.read', 'orders.create', 'orders.read', 'orders.bulk',
    'profile.read', 'profile.write', 'wholesale.read'
  ]
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Use React.useState to avoid dispatcher issues
  const [user, setUser] = React.useState<User | null>(null);
  const [sessionExpiry, setSessionExpiry] = React.useState<Date | null>(null);
  const [loading, setLoading] = React.useState(true);

  // Initialize auth state
  React.useEffect(() => {
    let isMounted = true;

    const initializeAuth = async () => {
      try {
        // Check if this is an email confirmation callback
        const { emailConfirmationService } = await import('../services/emailConfirmationService');

        if (emailConfirmationService.isEmailConfirmationCallback()) {
          console.log('🔐 Email confirmation callback detected');

          const confirmationResult = await emailConfirmationService.handleEmailConfirmation();

          if (confirmationResult.success && confirmationResult.user) {
            console.log('✅ Email confirmation successful, logging in user');
            if (isMounted) {
              setUser(confirmationResult.user);
              setLoading(false);
            }

            // Clean up URL
            emailConfirmationService.cleanupConfirmationUrl();
            return;
          } else {
            console.error('❌ Email confirmation failed:', confirmationResult.error);
            // Continue with normal session check
          }
        }

        // Check for existing Supabase session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Session check error:', error);
          if (isMounted) {
            setLoading(false);
          }
          return;
        }

        if (session?.user && isMounted) {
          // Try to get user profile from database
          const { data: userProfile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (profileError) {
            console.warn('❌ Profile not found in database for authenticated user:', session.user.id);
            console.warn('This indicates an incomplete registration. User must complete email confirmation.');

            // SECURITY: Do not allow dashboard access without database record
            // Sign out the user and force them to complete registration
            await supabase.auth.signOut();
            setUser(null);
            setSessionExpiry(null);

            // Show error message (you could also redirect to a specific page)
            console.error('Access denied: User profile not found. Please complete email confirmation or contact support.');
            return;
          } else {
            // Use database profile
            const dbUser: User = {
              id: userProfile.id,
              email: userProfile.email,
              fullName: userProfile.full_name,
              userType: userProfile.user_type as UserType,
              isActive: userProfile.is_active,
              createdAt: userProfile.created_at,
              updatedAt: userProfile.updated_at,
              phone: userProfile.phone || '',
              city: userProfile.city || 'Tetouan',
              companyName: userProfile.company_name,
              companyAddress: userProfile.company_address,
              permissions: defaultPermissions[userProfile.user_type as keyof typeof defaultPermissions] || defaultPermissions.client
            };
            setUser(dbUser);
          }

          // Set session expiry
          if (session.expires_at) {
            setSessionExpiry(new Date(session.expires_at * 1000));
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!isMounted) return;

        console.log('Auth state changed:', event);

        if (event === 'SIGNED_OUT' || !session) {
          console.log('🚪 Auth state: User signed out, performing cleanup...');
          setUser(null);
          setSessionExpiry(null);

          // Perform session cleanup on auth state change
          // Only if not already on login page to avoid infinite loops
          if (window.location.pathname !== '/' && !window.location.pathname.includes('auth')) {
            performCompleteSessionCleanup();
            // Navigate to login page
            setTimeout(() => {
              window.location.href = '/';
            }, 100);
          }
        } else if (event === 'SIGNED_IN' && session) {
          console.log('🔐 Auth state: User signed in, reinitializing...');
          // Reinitialize user data
          initializeAuth();
        }
      }
    );

    return () => {
      isMounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const login = React.useCallback((userData: User) => {
    setUser(userData);

    // Set session expiry (24 hours from now)
    const expiry = new Date();
    expiry.setHours(expiry.getHours() + 24);
    setSessionExpiry(expiry);

    // Update last login timestamp
    updateLastLogin(userData.id);
  }, []);

  const logout = React.useCallback(async () => {
    try {
      console.log('🚪 Starting logout process...');

      // Step 1: Sign out from Supabase
      await supabase.auth.signOut();

      // Step 2: Clear user state
      setUser(null);
      setSessionExpiry(null);

      // Step 3: Perform complete session cleanup
      performCompleteSessionCleanup();

      // Step 4: Force navigation to login page
      // Use setTimeout to ensure cleanup completes first
      setTimeout(() => {
        window.location.href = '/';
      }, 100);

      console.log('✅ Logout completed successfully');
    } catch (error) {
      console.error('❌ Logout error:', error);

      // Force logout even if Supabase fails
      setUser(null);
      setSessionExpiry(null);

      // Still perform cleanup and redirect
      performCompleteSessionCleanup();
      setTimeout(() => {
        window.location.href = '/';
      }, 100);
    }
  }, []);

  const updateUser = React.useCallback((userData: Partial<User>) => {
    setUser(prev => prev ? { ...prev, ...userData } : null);
  }, []);

  const hasPermission = React.useCallback(async (permission: string): Promise<boolean> => {
    if (!user) return false;

    // Import authorization service dynamically to avoid circular dependencies
    const { AuthorizationService } = await import('../services/authorizationService');

    try {
      // Use server-side permission validation
      const result = await AuthorizationService.hasPermission(permission as any);
      return result.authorized;
    } catch (error) {
      console.error('Permission check error:', error);
      // Fallback to client-side check for backward compatibility
      return user.permissions?.includes(permission) || false;
    }
  }, [user]);

  const isAuthenticated = React.useMemo(() => {
    return user !== null && user.isActive;
  }, [user]);

  const contextValue = React.useMemo(() => ({
    user,
    isAuthenticated,
    loading,
    login,
    logout,
    updateUser,
    hasPermission,
    sessionExpiry,
  }), [user, isAuthenticated, loading, login, logout, updateUser, hasPermission, sessionExpiry]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = React.useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
